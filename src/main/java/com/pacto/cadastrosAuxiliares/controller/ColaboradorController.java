package com.pacto.cadastrosAuxiliares.controller;

import com.pacto.cadastrosAuxiliares.dto.filtros.FiltroColaboradorJSON;
import com.pacto.cadastrosAuxiliares.services.interfaces.ColaboradorService;
import com.pacto.cadastrosAuxiliares.swagger.SwaggerTags;
import com.pacto.cadastrosAuxiliares.swagger.paginador.ExemploPaginadorRequisicaoSemOrdenacao;
import com.pacto.cadastrosAuxiliares.swagger.respostas.colaborador.ExemploRespostaListColaboradorPaginacao;
import com.pacto.config.dto.PaginadorDTO;
import com.pacto.config.exceptions.ServiceException;
import com.pacto.config.utils.EnvelopeRespostaDTO;
import com.pacto.config.utils.ResponseEntityFactory;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import org.json.JSONObject;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/colaboradores")
public class ColaboradorController {

    private final ColaboradorService colaboradorService;

    public ColaboradorController(ColaboradorService colaboradorService) {
        this.colaboradorService = colaboradorService;
    }


    @Operation(
            summary = "Consultar colaboradores",
            description = "Retorna uma lista de colaboradores da academia podendo ser filtrados por nome e por empresa que estão vinculados.",
            tags = {SwaggerTags.COLABORADORES},
            parameters = {
                    @Parameter(
                            name = "filters",
                            description = "Filtros de busca.\n\n" +
                                    "_Deve ser informado como um JSON e deve " +
                                    "ser realizado o encode na URL para a requisição funcionar._\n\n" +
                                    "**Filtros disponíveis**\n" +
                                    "- **searchTerm**: Filtra pelo nome do colaborador da academia\n" +
                                    "- **empresa**: Filtra pela empresa/unidade da academia que os colaboradores estão vinculados",
                            example = "{ \"searchTerm\": \"Maria Silva\", \"empresa\":1}",
                            schema = @Schema(implementation = String.class)
                    )
            },
            requestBody = @io.swagger.v3.oas.annotations.parameters.RequestBody(
                    description = "Atributos de paginação da resposta.",
                    required = true,
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = ExemploPaginadorRequisicaoSemOrdenacao.class,
                                    example = "{ \"page\": 0, \"size\": 10}")
                    )
            ),
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Solicitação bem-sucedida",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = ExemploRespostaListColaboradorPaginacao.class)
                            )
                    )
            }
    )
    @RequestMapping(value = "", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> findAll(@RequestParam(value = "filters", required = false) JSONObject filtros,
                                                       @RequestBody PaginadorDTO paginadorDTO) {
        try {
            FiltroColaboradorJSON filtroColaboradorJSON = new FiltroColaboradorJSON(filtros);
            return ResponseEntityFactory.ok(colaboradorService.findAll(filtroColaboradorJSON, paginadorDTO), paginadorDTO);
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }
}
