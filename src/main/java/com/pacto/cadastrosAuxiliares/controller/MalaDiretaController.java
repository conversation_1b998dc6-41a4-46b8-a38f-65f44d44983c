package com.pacto.cadastrosAuxiliares.controller;

import com.pacto.cadastrosAuxiliares.dto.basico.MalaDiretaDTO;
import com.pacto.config.exceptions.ServiceException;
import com.pacto.cadastrosAuxiliares.services.interfaces.MalaDiretaService;
import com.pacto.cadastrosAuxiliares.swagger.SwaggerTags;
import com.pacto.cadastrosAuxiliares.swagger.respostas.maladiretas.ExemploRespostaMalaDireta;
import com.pacto.cadastrosAuxiliares.swagger.respostas.maladiretas.ExemploRespostaMalaDiretaClonada;
import com.pacto.config.utils.EnvelopeRespostaDTO;
import com.pacto.config.utils.ResponseEntityFactory;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(value = {"/malaDireta", "/mala-direta"})
public class MalaDiretaController {
    private final MalaDiretaService malaDiretaService;

    public MalaDiretaController(MalaDiretaService malaDiretaService) {
        this.malaDiretaService = malaDiretaService;
    }

    @Operation(
            summary = "Consultar mala direta",
            description = "Consulta as informações de uma campanha de mala direta pelo código identificador dela.",
            tags = {SwaggerTags.MALA_DIRETA},
            parameters = {
                    @Parameter(name = "id", description = "Código identificador da campanha de mala direta que será consultada", example = "5", required = true),
            },
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Solicitação bem-sucedida",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = ExemploRespostaMalaDireta.class)
                            )
                    )
            }
    )
    @GetMapping(value = "/{id}")
    public ResponseEntity<EnvelopeRespostaDTO> findById(@PathVariable Integer id) {
        try {
            return ResponseEntityFactory.ok(malaDiretaService.malaDireta(id));
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @Operation(
            summary = "Clonar mala direta",
            description = "Clona uma campanha de mala direta existente, criando uma nova campanha com as mesmas configurações, filtros e agendamentos da campanha original.",
            tags = {SwaggerTags.MALA_DIRETA},
            parameters = {
                    @Parameter(name = "id", description = "Código identificador da campanha de mala direta que será clonada", example = "5", required = true),
            },
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Solicitação bem-sucedida",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = ExemploRespostaMalaDiretaClonada.class)
                            )
                    )
            }
    )
    @GetMapping(value = "/{id}/clonar")
    public ResponseEntity<EnvelopeRespostaDTO> clonarMalaDireta(@PathVariable Integer id) {
        try {
            return ResponseEntityFactory.ok(malaDiretaService.clonarMalaDireta(id));
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @Operation(
            summary = "Replicar mala direta",
            description = "Replica uma campanha de mala direta para todas as unidades da rede de academias, permitindo distribuir a mesma campanha de marketing para múltiplas filiais.",
            tags = {SwaggerTags.MALA_DIRETA},
            parameters = {
                    @Parameter(name = "empresaId", description = "Código identificador da empresa", required = true, example = "1", in = ParameterIn.HEADER)
            },
            requestBody = @io.swagger.v3.oas.annotations.parameters.RequestBody(
                    description = "Dados da campanha de mala direta que será replicada",
                    required = true,
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = MalaDiretaDTO.class)
                    )
            ),
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Solicitação bem-sucedida",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = ExemploRespostaMalaDireta.class)
                            )
                    )
            }
    )
    @PostMapping(value = "/replicar")
    public ResponseEntity<EnvelopeRespostaDTO> replicarFeriado(@RequestBody MalaDiretaDTO malaDiretaDTO) {
        try {
            malaDiretaDTO.setReplicar(true);
            return ResponseEntityFactory.ok(malaDiretaService.saveOrUpdate(malaDiretaDTO));
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }
}
