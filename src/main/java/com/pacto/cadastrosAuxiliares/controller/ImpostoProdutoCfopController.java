package com.pacto.cadastrosAuxiliares.controller;

import com.pacto.cadastrosAuxiliares.dto.basico.ImpostoProdutoCfopDTO;
import com.pacto.cadastrosAuxiliares.dto.filtros.FiltroImpostoProdutoJSON;
import com.pacto.cadastrosAuxiliares.services.interfaces.ImpostoProdutoCfopService;
import com.pacto.cadastrosAuxiliares.swagger.ExemploRespostaVazia;
import com.pacto.cadastrosAuxiliares.swagger.SwaggerTags;
import com.pacto.cadastrosAuxiliares.swagger.respostas.impostoproduto.ExemploRespostaConfigNotaFiscal;
import com.pacto.cadastrosAuxiliares.swagger.respostas.impostoproduto.ExemploRespostaImpostoProduto;
import com.pacto.cadastrosAuxiliares.swagger.respostas.impostoproduto.ExemploRespostaListImpostoProdutoPaginacao;
import com.pacto.cadastrosAuxiliares.swagger.respostas.impostoproduto.ExemploRespostaListLogsPaginacao;
import com.pacto.cadastrosAuxiliares.swagger.respostas.impostoproduto.ExemploRespostaQuantidadeProdutos;
import com.pacto.config.dto.PaginadorDTO;
import com.pacto.config.exceptions.ServiceException;
import com.pacto.config.utils.EnvelopeRespostaDTO;
import com.pacto.config.utils.ResponseEntityFactory;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import org.json.JSONObject;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/imposto-produto")
public class ImpostoProdutoCfopController {

    private final ImpostoProdutoCfopService service;

    public ImpostoProdutoCfopController(ImpostoProdutoCfopService service) {

        this.service = service;
    }

    @Operation(
            summary = "Consultar impostos de produtos",
            description = "Consulta todos os impostos de produtos e CFOP configurados para equipamentos e serviços de academia com paginação.",
            tags = {SwaggerTags.IMPOSTO},
            parameters = {
                    @Parameter(name = "page", description = "Número da página que será feita a requisição", example = "0", schema = @Schema(implementation = Integer.class)),
                    @Parameter(name = "size", description = "Quantidade máxima de elementos por página", example = "10", schema = @Schema(implementation = Integer.class)),
                    @Parameter(
                            name = "sort",
                            description = "Ordenação das respostas. Ordena as respostas por um dos atributos disponíveis no conteúdo da resposta.\n\n" +
                                    "**Ordens disponíveis**\n" +
                                    "- **asc**: Ordena de forma ascendente pelo atributo definido\n" +
                                    "- **desc**: Ordena de forma descendente pelo atributo definido\n" +
                                    "Para fazer a ordenação, utilize o padrão: **atributo,ordem**.\n\n" +
                                    "**Atributos disponíveis para ordenação**\n" +
                                    "- **codigo**: Código do imposto\n" +
                                    "- **cfop**: Código CFOP\n" +
                                    "- **ncm**: Código NCM\n" +
                                    "- **aliquotaICMS**: Alíquota do ICMS\n" +
                                    "- **aliquotaPIS**: Alíquota do PIS\n" +
                                    "- **aliquotaCOFINS**: Alíquota do COFINS",
                            example = "codigo,asc",
                            schema = @Schema(implementation = String.class)
                    ),
                    @Parameter(
                            name = "filters",
                            description = "Filtros de busca.\n\n" +
                                    "_Deve ser informado como um JSON e deve " +
                                    "ser realizado o encode na URL para a requisição funcionar._\n\n" +
                                    "**Filtros disponíveis**\n" +
                                    "- **quicksearchValue**: Filtra pelo código CFOP dos equipamentos de academia\n" +
                                    "- **situacao**: Filtra pela situação do imposto (true para ativo, false para desativado)",
                            example = "{\"quicksearchValue\":\"5102\",\"situacao\":[true]}",
                            schema = @Schema(implementation = String.class)
                    ),
                    @Parameter(name = "empresaId", description = "Código identificador da empresa/academia", example = "1", required = true)
            },
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Solicitação bem-sucedida",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = ExemploRespostaListImpostoProdutoPaginacao.class)
                            )
                    )
            }
    )
    @GetMapping()
    public ResponseEntity<EnvelopeRespostaDTO> todos(@RequestParam(value = "filters", required = false) JSONObject filtros,
                                                     @Parameter(hidden = true) PaginadorDTO paginadorDTO , @RequestHeader("empresaId") Integer empresaId) {
        try {
            FiltroImpostoProdutoJSON filtroImpostoProdutoJSON = new FiltroImpostoProdutoJSON(filtros,empresaId);
            return ResponseEntityFactory.ok(service.findAll(filtroImpostoProdutoJSON, paginadorDTO), paginadorDTO);
        } catch (ServiceException e) {
            StringBuilder result = new StringBuilder(e.toString() + "\n");
            StackTraceElement[] trace = e.getStackTrace();
            for (StackTraceElement stackTraceElement : trace) {
                result.append(stackTraceElement.toString()).append("\n");
            }
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(),  result.toString());
        }
    }

    @Operation(
            summary = "Consultar imposto de produto",
            description = "Consulta as informações de um imposto de produto específico pelo código identificador.",
            tags = {SwaggerTags.IMPOSTO},
            parameters = {
                    @Parameter(name = "id", description = "Código identificador do imposto de produto que será consultado", example = "1", required = true),
                    @Parameter(name = "empresaId", description = "Código identificador da empresa/academia", example = "1", required = false)
            },
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Solicitação bem-sucedida",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = ExemploRespostaImpostoProduto.class)
                            )
                    )
            }
    )
    @GetMapping("/{id}")
    public ResponseEntity<EnvelopeRespostaDTO> imposto(@PathVariable Integer id) {
        try {
            return ResponseEntityFactory.ok(service.findImpostoProduto(id));
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @Operation(
            summary = "Incluir imposto de produto",
            description = "Inclui ou atualiza um imposto de produto com configurações fiscais para equipamentos e serviços de academia.",
            tags = {SwaggerTags.IMPOSTO},
            parameters = {
                    @Parameter(name = "empresaId", description = "Código identificador da empresa/academia", example = "1", required = true)
            },
            requestBody = @io.swagger.v3.oas.annotations.parameters.RequestBody(
                    description = "Dados do imposto de produto a ser incluído ou atualizado",
                    required = true,
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = ImpostoProdutoCfopDTO.class)
                    )
            ),
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Solicitação bem-sucedida",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = ExemploRespostaImpostoProduto.class)
                            )
                    )
            }
    )
    @PostMapping
    public ResponseEntity<EnvelopeRespostaDTO> incluir(@RequestBody ImpostoProdutoCfopDTO dto,
                                                       @RequestHeader("empresaId") Integer empresaId) {
        try {
            return ResponseEntityFactory.ok(service.saveOrUpdate(dto, empresaId));
        } catch (ServiceException e) {
            return ResponseEntityFactory.mensagemFront(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e) {
            StringBuilder result = new StringBuilder(e.toString() + "\n");
            StackTraceElement[] trace = e.getStackTrace();
            for (StackTraceElement stackTraceElement : trace) {
                result.append(stackTraceElement.toString()).append("\n");
            }
            return ResponseEntityFactory.erroInterno(e.getMessage(),  result.toString());
        }

    }

    @Operation(
            summary = "Excluir imposto de produto",
            description = "Exclui um imposto de produto do sistema da academia pelo código identificador.",
            tags = {SwaggerTags.IMPOSTO},
            parameters = {
                    @Parameter(name = "id", description = "Código identificador do imposto de produto que será excluído", example = "1", required = true)
            },
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Solicitação bem-sucedida",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = ExemploRespostaVazia.class)
                            )
                    )
            }
    )
    @DeleteMapping("/{id}")
    public ResponseEntity<EnvelopeRespostaDTO> delete(@PathVariable Integer id ) {
        try {
            service.delete(id);
            return ResponseEntityFactory.ok();
        } catch (ServiceException e) {
            return ResponseEntityFactory.mensagemFront(e.getChaveExcecao(), e.getMessage());
        }
    }
    @Operation(
            summary = "Consultar configuração de nota fiscal",
            description = "Consulta as configurações de nota fiscal disponíveis para a academia, incluindo NFSe e NFCe.",
            tags = {SwaggerTags.IMPOSTO},
            parameters = {
                    @Parameter(name = "empresaId", description = "Código identificador da empresa/academia", example = "1", required = true)
            },
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Solicitação bem-sucedida",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = ExemploRespostaConfigNotaFiscal.class)
                            )
                    )
            }
    )
    @GetMapping("/config-notafiscal")
    public ResponseEntity<EnvelopeRespostaDTO> buscaConfigNotaFical(@RequestHeader("empresaId") Integer empresaId) {
        try {
            return ResponseEntityFactory.ok(service.findConfigNotaFiscal(empresaId));
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @Operation(
            summary = "Consultar quantidade de produtos atualizados",
            description = "Consulta a quantidade de produtos de academia que foram atualizados com as configurações de imposto especificadas.",
            tags = {SwaggerTags.IMPOSTO},
            parameters = {
                    @Parameter(name = "id", description = "Código identificador do imposto de produto", example = "1", required = true)
            },
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Solicitação bem-sucedida",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = ExemploRespostaQuantidadeProdutos.class)
                            )
                    )
            }
    )
    @GetMapping("/quantidade-produtos-atualizados/{id}")
    public ResponseEntity<EnvelopeRespostaDTO> buscaQuatidadeDeProdutosAtualizado(@PathVariable Integer id) {
        try {
            return ResponseEntityFactory.ok(service.findQuantidadeDeProdutosAtualizado(id));
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @Operation(
            summary = "Atualizar produtos com impostos",
            description = "Atualiza todos os produtos de academia que correspondem aos critérios CFOP e NCM com as configurações de imposto especificadas.",
            tags = {SwaggerTags.IMPOSTO},
            parameters = {
                    @Parameter(name = "empresaId", description = "Código identificador da empresa/academia", example = "1", required = true)
            },
            requestBody = @io.swagger.v3.oas.annotations.parameters.RequestBody(
                    description = "Dados do imposto de produto que será aplicado aos produtos da academia",
                    required = true,
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = ImpostoProdutoCfopDTO.class)
                    )
            ),
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Solicitação bem-sucedida",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = ExemploRespostaVazia.class)
                            )
                    )
            }
    )
    @PostMapping("/atualizar-produtos")
    public ResponseEntity<EnvelopeRespostaDTO> atualizaProdutos(@RequestBody ImpostoProdutoCfopDTO dto,
                                                       @RequestHeader("empresaId") Integer empresaId) {
        try {
            service.atualizaProdutos(dto, empresaId);
            return ResponseEntityFactory.ok();
        } catch (ServiceException e) {
            return ResponseEntityFactory.mensagemFront(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e) {
            StringBuilder result = new StringBuilder(e.toString() + "\n");
            StackTraceElement[] trace = e.getStackTrace();
            for (StackTraceElement stackTraceElement : trace) {
                result.append(stackTraceElement.toString()).append("\n");
            }
            return ResponseEntityFactory.erroInterno(e.getMessage(),  result.toString());
        }

    }

    @Operation(
            summary = "Consultar logs de impostos",
            description = "Consulta o histórico de alterações realizadas nos impostos de produtos da academia com paginação.",
            tags = {SwaggerTags.IMPOSTO},
            parameters = {
                    @Parameter(name = "page", description = "Número da página que será feita a requisição", example = "0", schema = @Schema(implementation = Integer.class)),
                    @Parameter(name = "size", description = "Quantidade máxima de elementos por página", example = "10", schema = @Schema(implementation = Integer.class)),
                    @Parameter(
                            name = "sort",
                            description = "Ordenação das respostas. Ordena as respostas por um dos atributos disponíveis no conteúdo da resposta.\n\n" +
                                    "**Ordens disponíveis**\n" +
                                    "- **asc**: Ordena de forma ascendente pelo atributo definido\n" +
                                    "- **desc**: Ordena de forma descendente pelo atributo definido\n" +
                                    "Para fazer a ordenação, utilize o padrão: **atributo,ordem**.\n\n" +
                                    "**Atributos disponíveis para ordenação**\n" +
                                    "- **operacao**: Tipo de operação realizada\n" +
                                    "- **usuario**: Usuário responsável pela alteração\n" +
                                    "- **dataAlteracao**: Data da alteração",
                            example = "dataAlteracao,desc",
                            schema = @Schema(implementation = String.class)
                    ),
                    @Parameter(
                            name = "filters",
                            description = "Filtros de busca.\n\n" +
                                    "_Deve ser informado como um JSON e deve " +
                                    "ser realizado o encode na URL para a requisição funcionar._\n\n" +
                                    "**Filtros disponíveis**\n" +
                                    "- **tipo**: Filtra pelo tipo de operação (INSERT, UPDATE, DELETE)\n" +
                                    "- **dataInicio**: Data inicial do período de consulta (timestamp)\n" +
                                    "- **dataFim**: Data final do período de consulta (timestamp)",
                            example = "{\"tipo\":[\"UPDATE\"],\"dataInicio\":1705276800000,\"dataFim\":1705363200000}",
                            schema = @Schema(implementation = String.class)
                    ),
                    @Parameter(name = "empresaId", description = "Código identificador da empresa/academia", example = "1", required = true)
            },
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Solicitação bem-sucedida",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = ExemploRespostaListLogsPaginacao.class)
                            )
                    )
            }
    )
    @GetMapping("/logs")
    public ResponseEntity<EnvelopeRespostaDTO> buscarLogs(
                                                          @Parameter(hidden = true) PaginadorDTO paginadorDTO,
                                                          @RequestParam(value = "filters", required = false) JSONObject filtros,
                                                          @RequestHeader("empresaId") Integer empresaId) {
        try {
            FiltroImpostoProdutoJSON filters = new FiltroImpostoProdutoJSON(filtros,empresaId);
            return ResponseEntityFactory.ok(service.buscarLogs( paginadorDTO, filters), paginadorDTO);
        } catch (ServiceException e) {
            return ResponseEntityFactory.mensagemFront(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e) {
            e.printStackTrace();
            StringBuilder result = new StringBuilder(e.toString() + "/n");
            StackTraceElement[] trace = e.getStackTrace();
            for (StackTraceElement stackTraceElement : trace) {
                result.append(stackTraceElement.toString()).append("/n");
            }
            return ResponseEntityFactory.erroInterno(e.getMessage(), result.toString());
        }
    }

}
