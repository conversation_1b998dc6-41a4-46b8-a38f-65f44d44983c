package com.pacto.cadastrosAuxiliares.controller;

import com.pacto.config.dto.PaginadorDTO;
import com.pacto.cadastrosAuxiliares.dto.basico.FeriadoDTO;
import com.pacto.cadastrosAuxiliares.dto.filtros.FiltroFeriadoJSON;
import com.pacto.config.exceptions.ServiceException;
import com.pacto.cadastrosAuxiliares.services.interfaces.FeriadoService;
import com.pacto.cadastrosAuxiliares.swagger.ExemploRespostaVazia;
import com.pacto.cadastrosAuxiliares.swagger.SwaggerTags;
import com.pacto.cadastrosAuxiliares.swagger.respostas.feriado.ExemploRespostaFeriado;
import com.pacto.cadastrosAuxiliares.swagger.respostas.feriado.ExemploRespostaListFeriadoPaginacao;
import com.pacto.config.utils.EnvelopeRespostaDTO;
import com.pacto.config.utils.ResponseEntityFactory;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import org.json.JSONObject;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/feriados")
public class FeriadoController {
    private final FeriadoService feriadoService;

    public FeriadoController(FeriadoService feriadoService) {
        this.feriadoService = feriadoService;
    }

    @Operation(
            summary = "Consultar feriados",
            description = "Consulta uma lista paginada de feriados cadastrados no sistema, incluindo feriados nacionais, estaduais e municipais. Permite aplicar filtros de busca para localizar feriados específicos que afetam o funcionamento das academias.",
            tags = {SwaggerTags.FERIADO},
            parameters = {
                    @Parameter(name = "page", description = "Número da página que será feita a requisição", example = "0", schema = @Schema(implementation = Integer.class)),
                    @Parameter(name = "size", description = "Quantidade máxima de elementos por página", example = "10", schema = @Schema(implementation = Integer.class)),
                    @Parameter(
                            name = "sort",
                            description = "Ordenação das respostas. Ordena as respostas por um dos atributos disponíveis no conteúdo da resposta.\n\n" +
                                    "**Ordens disponíveis**\n" +
                                    "- **asc**: Ordena de forma ascendente pelo atributo definido\n" +
                                    "- **desc**: Ordena de forma descendente pelo atributo definido\n" +
                                    "Para fazer a ordenação, utilize o padrão: **atributo,ordem**.\n\n" +
                                    "**Atributos disponíveis para ordenação**\n" +
                                    "- **codigo**: Ordena pelo código do feriado\n" +
                                    "- **descricao**: Ordena pelo nome do feriado\n" +
                                    "- **dia**: Ordena pela data do feriado\n" +
                                    "- **mes**: Ordena pelo mês do feriado\n" +
                                    "- **nacional**: Ordena por feriados nacionais\n" +
                                    "- **estadual**: Ordena por feriados estaduais\n" +
                                    "- **naoRecorrente**: Ordena por feriados não recorrentes\n",
                            example = "descricao,asc",
                            schema = @Schema(implementation = String.class)
                    ),
                    @Parameter(
                            name = "filters",
                            description = "Filtros de busca.\n\n" +
                                    "_Deve ser informado como um JSON e deve " +
                                    "ser realizado o encode na URL para a requisição funcionar._\n\n" +
                                    "**Filtros disponíveis**\n" +
                                    "- **quicksearchValue**: Filtra pelo nome ou código do feriado (ex: \"Natal\", \"Ano Novo\")\n" +
                                    "- **estado**: Filtra por feriados de um estado específico (código do estado)\n" +
                                    "- **cidade**: Filtra por feriados de uma cidade específica (código da cidade)\n" +
                                    "- **pais**: Filtra por feriados de um país específico (código do país)",
                            example = "{\"quicksearchValue\":\"natal\",\"estado\":1}",
                            schema = @Schema(implementation = String.class)
                    ),
            },
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Solicitação bem-sucedida",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = ExemploRespostaListFeriadoPaginacao.class)
                            )
                    )
            }
    )
    @GetMapping()
    public ResponseEntity<EnvelopeRespostaDTO> todos(@RequestParam(value = "filters", required = false) JSONObject filtros,
                                                     @Parameter(hidden = true) PaginadorDTO paginadorDTO) {
        try {
            FiltroFeriadoJSON filtroFeriadoJSON = new FiltroFeriadoJSON(filtros);
            return ResponseEntityFactory.ok(feriadoService.findAll(filtroFeriadoJSON, paginadorDTO), paginadorDTO);
        } catch (ServiceException e) {
            StringBuilder result = new StringBuilder(e.toString() + "\n");
            StackTraceElement[] trace = e.getStackTrace();
            for (StackTraceElement stackTraceElement : trace) {
                result.append(stackTraceElement.toString()).append("\n");
            }
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(),  result.toString());
        }
    }

    @Operation(
            summary = "Consultar feriado",
            description = "Consulta as informações de um feriado específico pelo código identificador.",
            tags = {SwaggerTags.FERIADO},
            parameters = {
                    @Parameter(name = "id", description = "Código identificador do feriado que será consultado", example = "1", required = true),
            },
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Solicitação bem-sucedida",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = ExemploRespostaFeriado.class)
                            )
                    )
            }
    )
    @GetMapping(value = "/{id}")
    public ResponseEntity<EnvelopeRespostaDTO> feriado(@PathVariable Integer id) {
        try {
            return ResponseEntityFactory.ok(feriadoService.feriado(id));
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @Operation(
            summary = "Clonar feriado",
            description = "Cria uma cópia de um feriado existente com base no código identificador fornecido. O feriado clonado terá um novo código e a descrição será prefixada com 'Cópia de'.",
            tags = {SwaggerTags.FERIADO},
            parameters = {
                    @Parameter(name = "id", description = "Código identificador do feriado que será clonado", example = "1", required = true),
            },
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Solicitação bem-sucedida",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = ExemploRespostaFeriado.class)
                            )
                    )
            }
    )
    @GetMapping(value = "/{id}/clonar")
    public ResponseEntity<EnvelopeRespostaDTO> clonarFeriado(@PathVariable Integer id) {
        try {
            return ResponseEntityFactory.ok(feriadoService.clonarFeriado(id));
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @Operation(
            summary = "Replicar feriado",
            description = "Replica um feriado existente para outras localidades ou períodos. Esta operação permite aplicar um feriado já cadastrado em diferentes contextos.",
            tags = {SwaggerTags.FERIADO},
            requestBody = @io.swagger.v3.oas.annotations.parameters.RequestBody(
                    description = "Dados do feriado que será replicado",
                    required = true,
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = FeriadoDTO.class)
                    )
            ),
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Solicitação bem-sucedida",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = ExemploRespostaFeriado.class)
                            )
                    )
            }
    )
    @PostMapping(value = "/replicar")
    public ResponseEntity<EnvelopeRespostaDTO> replicarFeriado(@RequestBody FeriadoDTO feriado) {
        try {
            feriado.setReplicar(true);
            return ResponseEntityFactory.ok(feriadoService.saveOrUpdate(feriado));
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @Operation(
            summary = "Incluir ou atualizar feriado",
            description = "Inclui um novo feriado ou atualiza um feriado existente. Se o código não for informado, um novo feriado será criado. Se o código for informado, o feriado existente será atualizado.",
            tags = {SwaggerTags.FERIADO},
            requestBody = @io.swagger.v3.oas.annotations.parameters.RequestBody(
                    description = "Dados do feriado que será incluído ou atualizado",
                    required = true,
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = FeriadoDTO.class)
                    )
            ),
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Solicitação bem-sucedida",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = ExemploRespostaFeriado.class)
                            )
                    )
            }
    )
    @ResponseBody
    @RequestMapping(value = "", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<EnvelopeRespostaDTO> incluirFeriado(@RequestBody FeriadoDTO feriadoDTO) {
        try {
            return ResponseEntityFactory.ok(feriadoService.saveOrUpdate(feriadoDTO));
        } catch (ServiceException e) {
            return ResponseEntityFactory.mensagemFront(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e) {
            StringBuilder result = new StringBuilder(e.toString() + "\n");
            StackTraceElement[] trace = e.getStackTrace();
            for (StackTraceElement stackTraceElement : trace) {
                result.append(stackTraceElement.toString()).append("\n");
            }
            return ResponseEntityFactory.erroInterno(e.getMessage(),  result.toString());
        }

    }

    @Operation(
            summary = "Deletar feriado",
            description = "Remove um feriado do sistema com base no código identificador fornecido.",
            tags = {SwaggerTags.FERIADO},
            parameters = {
                    @Parameter(name = "id", description = "Código identificador do feriado que será removido", example = "1", required = true),
            },
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Solicitação bem-sucedida",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = ExemploRespostaVazia.class)
                            )
                    )
            }
    )
    @DeleteMapping("/{id}")
    public ResponseEntity<EnvelopeRespostaDTO> deletarFeriado(@PathVariable Integer id ) {
        try {
            feriadoService.delete(id);
            return ResponseEntityFactory.ok();
        } catch (ServiceException e) {
            return ResponseEntityFactory.mensagemFront(e.getChaveExcecao(), e.getMessage());
        }
    }
}
