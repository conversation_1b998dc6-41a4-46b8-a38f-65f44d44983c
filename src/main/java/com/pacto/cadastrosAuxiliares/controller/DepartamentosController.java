package com.pacto.cadastrosAuxiliares.controller;

import com.pacto.cadastrosAuxiliares.dto.basico.DepartamentoDTO;
import com.pacto.cadastrosAuxiliares.dto.filtros.FiltroDepartamentoJSON;
import com.pacto.cadastrosAuxiliares.services.interfaces.DepartamentoService;
import com.pacto.cadastrosAuxiliares.swagger.ExemploRespostaVazia;
import com.pacto.cadastrosAuxiliares.swagger.SwaggerTags;
import com.pacto.cadastrosAuxiliares.swagger.respostas.departamento.ExemploRespostaDepartamento;
import com.pacto.cadastrosAuxiliares.swagger.respostas.departamento.ExemploRespostaListDepartamentoPaginacao;
import com.pacto.config.dto.PaginadorDTO;
import com.pacto.config.exceptions.ServiceException;
import com.pacto.config.utils.EnvelopeRespostaDTO;
import com.pacto.config.utils.ResponseEntityFactory;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import org.json.JSONObject;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/departamentos")
public class DepartamentosController {
    private final DepartamentoService departamentoService;

    public DepartamentosController(DepartamentoService departamentoService) {
        this.departamentoService = departamentoService;
    }

    @Operation(
            summary = "Cadastrar departamento",
            description = "Cadastra um novo departamento.",
            tags = {SwaggerTags.DEPARTAMENTO},
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Solicitação bem-sucedida",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = ExemploRespostaDepartamento.class)
                            )
                    )
            }
    )
    @PostMapping
    public ResponseEntity<EnvelopeRespostaDTO> incluirDepartamentos(@RequestBody DepartamentoDTO departamentos) {
        try {
            return ResponseEntityFactory.ok(departamentoService.saveOrUpdate(departamentos));
        } catch (ServiceException e) {
            return ResponseEntityFactory.mensagemFront(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e) {
            StringBuilder result = new StringBuilder(e.toString() + "/n");
            StackTraceElement[] trace = e.getStackTrace();
            for (StackTraceElement stackTraceElement : trace) {
                result.append(stackTraceElement.toString()).append("/n");
            }
            return ResponseEntityFactory.erroInterno(e.getMessage(), result.toString());
        }
    }


    @Operation(
            summary = "Consultar departamentos",
            description = "Consulta os departamentos cadastrados, podendo aplicar filtros para a consulta.",
            tags = {SwaggerTags.DEPARTAMENTO},
            parameters = {
                    @Parameter(
                            name = "filters",
                            description = "Filtros de busca.\n\n" +
                                    "_Deve ser informado como um JSON e deve " +
                                    "ser realizado o encode na URL para a requisição funcionar._\n\n" +
                                    "**Filtros disponíveis**\n" +
                                    "- **quicksearchValue**: Filtra pelo nome ou pelo código do departamento",
                            example = "{\"quicksearchValue\":\"financeiro\"}",
                            schema = @Schema(implementation = String.class)
                    ),
                    @Parameter(name = "page", description = "Número da página que será feita a requisição", example = "0", schema = @Schema(implementation = Integer.class)),
                    @Parameter(name = "size", description = "Quantidade máxima de elementos por página", example = "10", schema = @Schema(implementation = Integer.class)),
                    @Parameter(
                            name = "sort",
                            description = "Ordenação das respostas. Ordena as respostas por um dos atributos disponíveis na conteúdo da resposta.\n\n" +
                                    "**Ordens disponíveis**\n" +
                                    "- **asc**: Ordena de forma ascendente pelo atributo definido\n" +
                                    "- **desc**: Ordena de forma descendente pelo atributo definido\n" +
                                    "Para fazer a ordenação, utilize o padrão: **atributo,ordem**.\n",
                            example = "nome,asc",
                            schema = @Schema(implementation = String.class)
                    )
            },
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Solicitação bem-sucedida",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = ExemploRespostaListDepartamentoPaginacao.class)
                            )
                    )
            }
    )
    @GetMapping()
    public ResponseEntity<EnvelopeRespostaDTO> todos(@RequestParam(value = "filters", required = false) JSONObject filtros,
                                                     @Parameter(hidden = true) PaginadorDTO paginadorDTO) {
        try {
            FiltroDepartamentoJSON filtroDepartamentoJSON = new FiltroDepartamentoJSON(filtros);
            return ResponseEntityFactory.ok(departamentoService.findAll(filtroDepartamentoJSON, paginadorDTO), paginadorDTO);
        } catch (ServiceException e) {
            StringBuilder result = new StringBuilder(e.toString() + "\n");
            StackTraceElement[] trace = e.getStackTrace();
            for (StackTraceElement stackTraceElement : trace) {
                result.append(stackTraceElement.toString()).append("\n");
            }
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), result.toString());
        }
    }


    @Operation(
            summary = "Consultar departamento",
            description = "Consulta as informações de um departamento pelo código identificador dele.",
            tags = {SwaggerTags.DEPARTAMENTO},
            parameters = {
                    @Parameter(name = "id", description = "Código identificador do departamento que será consultado", example = "12", required = true),
            },
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Solicitação bem-sucedida",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = ExemploRespostaListDepartamentoPaginacao.class)
                            )
                    )
            }
    )
    @GetMapping("/{id}")
    public ResponseEntity<EnvelopeRespostaDTO> departamento(@PathVariable Integer id) {
        try {
            return ResponseEntityFactory.ok(departamentoService.departamento(id));
        } catch (ServiceException e) {
            return ResponseEntityFactory.erroInterno(e.getChaveExcecao(), e.getMessage());
        }
    }

    @Operation(
            summary = "Deletar um departamento",
            description = "Exclui as informações de um departamento pelo código identificador dele.",
            tags = {SwaggerTags.DEPARTAMENTO},
            parameters = {
                    @Parameter(name = "id", description = "Código identificador do departamento que será excluído", example = "12", required = true),
            },
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Solicitação bem-sucedida. (Não retorna corpo de resposta)",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = ExemploRespostaVazia.class)
                            )
                    )
            }
    )
    @DeleteMapping("/{id}")
    public ResponseEntity<EnvelopeRespostaDTO> deletar(@PathVariable Integer id) {
        try {
            departamentoService.delete(id);
            return ResponseEntityFactory.ok();
        } catch (ServiceException e) {
            return ResponseEntityFactory.mensagemFront(e.getChaveExcecao(), e.getMessage());
        }
    }
}
